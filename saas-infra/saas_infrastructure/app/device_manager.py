# device_manager.py

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from utils.tenant_aware_database import TenantAwareDatabase

class DeviceManager:
    def __init__(self):
        self.engine = self.create_engine()
        self.Session = sessionmaker(bind=self.engine)

    def create_engine(self):
        db_url = os.getenv("DATABASE_URL")
        return create_engine(db_url)

    def get_session(self):
        return self.Session()

    def add_device(self, device_data, tenant_id):
        session = self.get_session()
        try:
            device = TenantAwareDatabase.add_device(session, device_data, tenant_id)
            session.commit()
            return device
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_devices(self, tenant_id):
        session = self.get_session()
        try:
            return TenantAwareDatabase.get_devices(session, tenant_id)
        finally:
            session.close()

    def update_device(self, device_id, device_data, tenant_id):
        session = self.get_session()
        try:
            device = TenantAwareDatabase.update_device(session, device_id, device_data, tenant_id)
            session.commit()
            return device
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def delete_device(self, device_id, tenant_id):
        session = self.get_session()
        try:
            TenantAwareDatabase.delete_device(session, device_id, tenant_id)
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()