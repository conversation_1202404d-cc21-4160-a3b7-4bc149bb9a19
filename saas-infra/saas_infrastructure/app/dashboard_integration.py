from flask import Blueprint, render_template, request, redirect, url_for, flash
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
import os

dashboard_bp = Blueprint('dashboard', __name__)

# Database connection setup
def get_db_connection():
    db_url = os.getenv('DATABASE_URL')
    engine = create_engine(db_url)
    Session = sessionmaker(bind=engine)
    return Session()

@dashboard_bp.route('/dashboard')
def dashboard():
    session = get_db_connection()()
    # Fetch tenant-specific data for the dashboard
    # Example: tenant_data = session.query(TenantModel).filter_by(tenant_id=current_tenant_id).all()
    # Render the dashboard template with the fetched data
    return render_template('dashboard.html')

@dashboard_bp.route('/dashboard/settings', methods=['GET', 'POST'])
def dashboard_settings():
    if request.method == 'POST':
        # Handle settings update
        # Example: update_settings(request.form)
        flash('Settings updated successfully!', 'success')
        return redirect(url_for('dashboard.dashboard_settings'))
    
    # Fetch current settings to display in the form
    # Example: current_settings = get_current_settings()
    return render_template('dashboard_settings.html')