class TemplateAdapter:
    def __init__(self, template_engine):
        self.template_engine = template_engine

    def render_template(self, template_name, context):
        """
        Renders a template with the given context.
        
        :param template_name: Name of the template to render.
        :param context: Dictionary containing context variables for the template.
        :return: Rendered template as a string.
        """
        return self.template_engine.render(template_name, **context)

    def get_template(self, template_name):
        """
        Retrieves a template by name.
        
        :param template_name: Name of the template to retrieve.
        :return: Template object.
        """
        return self.template_engine.get_template(template_name)

    def add_filter(self, filter_name, filter_function):
        """
        Adds a custom filter to the template engine.
        
        :param filter_name: Name of the filter to add.
        :param filter_function: Function that implements the filter logic.
        """
        self.template_engine.add_filter(filter_name, filter_function)