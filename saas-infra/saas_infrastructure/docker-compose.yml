version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=postgresql://user:pass@host:port/db
      - FLASK_SECRET_KEY=your_secret_key
      - JWT_SECRET_KEY=your_jwt_secret
      - CLOUDFLARE_TUNNEL_TOKEN=your_tunnel_token
    volumes:
      - ./app:/app
      - ./database:/database
      - ./bridge:/bridge
    depends_on:
      - db

  db:
    image: postgres:latest
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: mobile_automation_saas
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data: