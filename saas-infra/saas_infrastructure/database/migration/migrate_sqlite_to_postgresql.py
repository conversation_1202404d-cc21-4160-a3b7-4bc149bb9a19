from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
import sqlite3
import psycopg2

# Database connection settings
SQLITE_DB_PATH = 'path_to_your_sqlite_db.sqlite'
POSTGRES_DB_URL = os.getenv('DATABASE_URL')

# Create PostgreSQL engine
postgres_engine = create_engine(POSTGRES_DB_URL)
Session = sessionmaker(bind=postgres_engine)

def migrate_data():
    # Connect to SQLite
    sqlite_conn = sqlite3.connect(SQLITE_DB_PATH)
    sqlite_cursor = sqlite_conn.cursor()

    # Connect to PostgreSQL
    postgres_session = Session()

    try:
        # Example migration: Migrate test suites
        sqlite_cursor.execute("SELECT * FROM test_suites")
        test_suites = sqlite_cursor.fetchall()

        for suite in test_suites:
            # Assuming the test_suites table has columns: id, name, created_at
            postgres_session.execute(
                "INSERT INTO tenant_test_suites (id, name, created_at, tenant_id) VALUES (%s, %s, %s, %s)",
                (suite[0], suite[1], suite[2], 'default_tenant_id')  # Replace with actual tenant_id
            )

        # Commit the transaction
        postgres_session.commit()

    except Exception as e:
        print(f"Error during migration: {e}")
        postgres_session.rollback()

    finally:
        # Close connections
        sqlite_conn.close()
        postgres_session.close()

if __name__ == "__main__":
    migrate_data()