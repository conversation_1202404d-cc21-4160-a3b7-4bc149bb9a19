from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os

def init_db():
    # Load database URL from environment variable
    database_url = os.getenv('DATABASE_URL')
    
    # Create a new SQLAlchemy engine
    engine = create_engine(database_url)
    
    # Create a configured "Session" class
    Session = sessionmaker(bind=engine)
    
    # Create a session
    session = Session()
    
    # Initialize the database schema
    with engine.connect() as connection:
        with open('saas_infrastructure/database/schema.sql', 'r') as f:
            schema_sql = f.read()
            connection.execute(schema_sql)
    
    # Commit the session
    session.commit()
    
    # Close the session
    session.close()

if __name__ == "__main__":
    init_db()