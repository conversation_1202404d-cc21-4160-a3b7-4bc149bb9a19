# File: /saas_infrastructure/deployment/deploy.py

import os
import subprocess
import sys

def run_command(command):
    """Run a shell command and return the output."""
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        sys.exit(result.returncode)
    return result.stdout

def setup_environment():
    """Set up the environment for deployment."""
    print("Setting up environment...")
    run_command("sudo ./setup_environment.sh")

def run_migrations():
    """Run database migrations using Alembic."""
    print("Running database migrations...")
    run_command("alembic upgrade head")

def deploy_application():
    """Deploy the application."""
    print("Deploying application...")
    run_command("sudo deploy-saas --step all")

def validate_deployment():
    """Validate the deployment."""
    print("Validating deployment...")
    run_command("python3 ../tests/test_deployment.py --test all")

def main():
    """Main deployment function."""
    setup_environment()
    run_migrations()
    deploy_application()
    validate_deployment()
    print("Deployment completed successfully.")

if __name__ == "__main__":
    main()