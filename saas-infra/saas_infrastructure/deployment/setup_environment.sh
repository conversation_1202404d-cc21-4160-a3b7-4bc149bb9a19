#!/bin/bash

# This script sets up the environment for the Mobile Automation SaaS Platform deployment.

# Update package lists
sudo apt-get update

# Install required packages
sudo apt-get install -y python3 python3-pip python3-venv postgresql postgresql-contrib

# Create a Python virtual environment
python3 -m venv venv

# Activate the virtual environment
source venv/bin/activate

# Install required Python packages
pip install -r app/requirements.txt

# Set up PostgreSQL database
sudo -u postgres psql -c "CREATE DATABASE mobile_automation_saas;"
sudo -u postgres psql -c "CREATE USER saas_user WITH PASSWORD 'your_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mobile_automation_saas TO saas_user;"

# Initialize the database schema
python3 database/init_db.py

# Run Alembic migrations
alembic upgrade head

# Deactivate the virtual environment
deactivate

echo "Environment setup complete. You can now run the application."