# Deployment README

## Overview

This README provides essential information regarding the deployment of the Mobile Automation SaaS Platform, focusing on the migration from an existing SQLite database to a multi-tenant PostgreSQL architecture. The migration plan aims to enhance scalability, security, and performance while ensuring tenant isolation.

## Migration Plan

### 1. Database Migration

The migration from SQLite to PostgreSQL involves several key components:

- **Alembic Migrations**: Utilize Alembic for managing database schema migrations. The configuration file `migrations/alembic.ini` will define the PostgreSQL connection string and migration settings.
- **Initial Schema Migration**: The initial migration script `migrations/versions/001_initial_saas_schema.py` will establish the complete SaaS schema, including tenant management tables and audit logging.
- **Legacy Data Migration**: The script `migrations/data_migration/legacy_data_migrator.py` will handle the transfer of existing data from SQLite to PostgreSQL, ensuring proper tenant context.

### 2. Configuration Changes

- **Database Connection Management**: The new `app/utils/database_connection_manager.py` will manage connections for both SQLite and PostgreSQL, adapting to the `SAAS_MODE` configuration.
- **Tenant-Aware Database Layer**: The `app/utils/tenant_aware_database.py` will provide a multi-tenant API, ensuring that all database operations respect tenant isolation.

### 3. Documentation and Validation

- **Migration Guide**: A comprehensive guide `docs/migration_guide.md` will detail the migration process, including prerequisites, configuration changes, and troubleshooting tips.
- **Validation Scripts**: Post-migration validation will be conducted using `scripts/validate_migration.py` to ensure data integrity and application functionality.

## Deployment Steps

1. **Prepare Environment**: Ensure that the PostgreSQL server is set up and accessible. Update the environment variables for database connections.
2. **Run Migrations**: Execute the Alembic migrations to set up the initial schema.
3. **Migrate Legacy Data**: Use the legacy data migrator script to transfer existing data into the new PostgreSQL structure.
4. **Validate Migration**: Run validation scripts to confirm the integrity and functionality of the migrated data.
5. **Update Configuration**: Modify application settings to support the new multi-tenant architecture.

## Conclusion

This migration plan is designed to facilitate a smooth transition from SQLite to a robust PostgreSQL architecture, ensuring that the Mobile Automation SaaS Platform can scale effectively while maintaining high standards of security and performance.