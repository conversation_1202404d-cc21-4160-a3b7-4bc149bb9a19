FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY app/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ .

# Copy database migration scripts
COPY database/ /app/database/

# Copy bridge service
COPY bridge/ /app/bridge/

# Copy deployment scripts
COPY deployment/ /app/deployment/

# Copy tests
COPY tests/ /app/tests/

# Set the entry point for the container
CMD ["python", "saas_app.py"]