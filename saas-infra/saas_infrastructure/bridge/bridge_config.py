# bridge_config.py

import os

class BridgeConfig:
    def __init__(self):
        self.device_bridge_host = os.getenv('DEVICE_BRIDGE_HOST', 'localhost')
        self.device_bridge_port = int(os.getenv('DEVICE_BRIDGE_PORT', 5001))
        self.cloudflare_tunnel_token = os.getenv('CLOUDFLARE_TUNNEL_TOKEN', '')
        self.tenant_id = os.getenv('TENANT_ID', 'default_tenant')

    def get_bridge_url(self):
        return f'ws://{self.device_bridge_host}:{self.device_bridge_port}/ws'

    def validate(self):
        if not self.cloudflare_tunnel_token:
            raise ValueError("Cloudflare tunnel token is required.")
        if not self.tenant_id:
            raise ValueError("Tenant ID is required.")

# Example usage
if __name__ == "__main__":
    config = BridgeConfig()
    config.validate()
    print(f"Bridge URL: {config.get_bridge_url()}")