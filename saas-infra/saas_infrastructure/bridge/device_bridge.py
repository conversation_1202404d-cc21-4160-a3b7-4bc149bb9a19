# device_bridge.py

import os
import json
import logging
import asyncio
import websockets

from utils.database_connection_manager import get_database_connection
from utils.tenant_aware_database import TenantAwareDatabase

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def handle_device_connection(websocket, path):
    tenant_id = path.strip("/")
    db_connection = get_database_connection()
    tenant_db = TenantAwareDatabase(db_connection, tenant_id)

    try:
        async for message in websocket:
            logger.info(f"Received message from tenant {tenant_id}: {message}")
            response = await process_message(message, tenant_db)
            await websocket.send(response)
    except Exception as e:
        logger.error(f"Error handling connection for tenant {tenant_id}: {e}")
    finally:
        await db_connection.close()

async def process_message(message, tenant_db):
    # Process the incoming message and interact with the tenant database
    # This is a placeholder for actual message processing logic
    return json.dumps({"status": "success", "data": message})

def start_device_bridge():
    server = websockets.serve(handle_device_connection, "localhost", 8765)
    logger.info("Starting device bridge on ws://localhost:8765")
    asyncio.get_event_loop().run_until_complete(server)
    asyncio.get_event_loop().run_forever()

if __name__ == "__main__":
    start_device_bridge()