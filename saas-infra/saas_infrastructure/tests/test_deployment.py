# test_deployment.py

import os
import unittest
from app.utils.database_connection_manager import get_db_connection
from app.utils.tenant_aware_database import TenantAwareDatabase

class TestDeployment(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        cls.db_connection = get_db_connection()
        cls.tenant_db = TenantAwareDatabase(cls.db_connection)

    def test_database_connection(self):
        self.assertIsNotNone(self.db_connection)
        self.assertTrue(self.db_connection.is_connected())

    def test_tenant_context(self):
        tenant_id = 'default_tenant'
        self.tenant_db.set_tenant(tenant_id)
        self.assertEqual(self.tenant_db.current_tenant, tenant_id)

    def test_schema_migration(self):
        # Assuming we have a function to check schema version
        current_version = self.tenant_db.get_schema_version()
        self.assertEqual(current_version, '002')  # Check if the latest migration is applied

    def test_data_integrity(self):
        # Check if data is migrated correctly
        tenant_id = 'default_tenant'
        test_suites = self.tenant_db.get_test_suites(tenant_id)
        self.assertGreater(len(test_suites), 0)  # Ensure there are test suites for the tenant

    def test_row_level_security(self):
        tenant_id = 'default_tenant'
        self.tenant_db.set_tenant(tenant_id)
        test_cases = self.tenant_db.get_test_cases()
        for case in test_cases:
            self.assertEqual(case['tenant_id'], tenant_id)  # Ensure RLS is enforced

    @classmethod
    def tearDownClass(cls):
        cls.db_connection.close()

if __name__ == '__main__':
    unittest.main()